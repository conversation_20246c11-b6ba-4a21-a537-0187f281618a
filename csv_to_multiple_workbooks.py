import pandas as pd
import os
import glob
from pathlib import Path
import shutil
from datetime import datetime



def copy_csv_to_multiple_workbooks(csv_file_path, excel_template_path, max_rows_per_workbook=10000):
    """
    Copy data from CSV file to multiple Excel workbooks with row limit per workbook.
    Each workbook will have the same Worksheet 2 (configuration) from the template.
    
    Args:
        csv_file_path (str): Path to the CSV file
        excel_template_path (str): Path to the template Excel file
        max_rows_per_workbook (int): Maximum rows per workbook (default: 10000)
    """
    try:
        # Read the CSV file
        print(f"Reading CSV file: {csv_file_path}")
        df_csv = pd.read_csv(csv_file_path)
        print(f"CSV contains {len(df_csv)} rows and {len(df_csv.columns)} columns")
        print(f"Columns: {list(df_csv.columns)}")
        
        # Check if template Excel file exists
        if not os.path.exists(excel_template_path):
            print(f"Error: Template Excel file not found: {excel_template_path}")
            return False
        
        # Read the template Excel file to get all sheets
        print(f"Reading template Excel file: {excel_template_path}")
        xl_file = pd.ExcelFile(excel_template_path)

        # Read all sheets from template to preserve them
        template_sheets = {}
        for sheet_name in xl_file.sheet_names:
            template_sheets[sheet_name] = pd.read_excel(excel_template_path, sheet_name=sheet_name)
            print(f"Read template sheet '{sheet_name}' with shape {template_sheets[sheet_name].shape}")

        # Get the headers from Worksheet 1 in template (should be 'Name', 'Email')
        worksheet1_template = template_sheets.get('Worksheet 1', pd.DataFrame())
        if not worksheet1_template.empty:
            original_headers = list(worksheet1_template.columns)
            print(f"Template headers: {original_headers}")
        else:
            original_headers = ['Name', 'Email']  # Default fallback
            print(f"Using default headers: {original_headers}")

        # Map CSV data to template structure
        # Assume first 2 columns of CSV map to Name, Email
        csv_columns = list(df_csv.columns)
        if len(csv_columns) >= 2 and len(original_headers) >= 2:
            # Create new dataframe with template headers
            df_csv_mapped = pd.DataFrame()
            df_csv_mapped[original_headers[0]] = df_csv.iloc[:, 0]  # First CSV column -> Name
            df_csv_mapped[original_headers[1]] = df_csv.iloc[:, 1]  # Second CSV column -> Email
            print(f"Mapped CSV columns [{csv_columns[0]}, {csv_columns[1]}] to template headers {original_headers[:2]}")
        else:
            print("Warning: Column mapping issue, using CSV as-is")
            df_csv_mapped = df_csv
        
        # Calculate number of workbooks needed
        total_rows = len(df_csv_mapped)
        num_workbooks = (total_rows + max_rows_per_workbook - 1) // max_rows_per_workbook
        print(f"Will create {num_workbooks} workbook(s) for {total_rows} rows")
        
        # Get the directory and base name of the template file
        template_dir = os.path.dirname(excel_template_path)
        template_name = os.path.splitext(os.path.basename(excel_template_path))[0]

        # Get current date and time in format: YYYYMMDD_HHMMSS
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        created_files = []

        for workbook_num in range(num_workbooks):
            # Calculate row range for this workbook
            start_row = workbook_num * max_rows_per_workbook
            end_row = min(start_row + max_rows_per_workbook, total_rows)

            # Create filename for this workbook with date and time
            output_file = os.path.join(template_dir, f"{template_name}_{timestamp}_{workbook_num + 1}.xlsx")
            
            print(f"\nCreating workbook {workbook_num + 1}: {output_file}")
            print(f"Rows {start_row + 1} to {end_row} (total: {end_row - start_row} rows)")
            
            # Get data slice for this workbook
            data_slice = df_csv_mapped.iloc[start_row:end_row]

            # Create the workbook using pandas only (preserves original formatting)
            with pd.ExcelWriter(output_file, engine='xlsxwriter') as writer:

                # Write data to Worksheet 1
                data_slice.to_excel(writer, sheet_name='Worksheet 1', index=False)
                print(f"  Written {len(data_slice)} rows to 'Worksheet 1'")

                # Copy other sheets from template
                for sheet_name, sheet_data in template_sheets.items():
                    if sheet_name != 'Worksheet 1':
                        sheet_data.to_excel(writer, sheet_name=sheet_name, index=False)
                        print(f"  Copied '{sheet_name}' from template")

                # Create empty Worksheet 3 with same headers if it doesn't exist
                if 'Worksheet 3' not in template_sheets:
                    empty_df = pd.DataFrame(columns=original_headers[:2])
                    empty_df.to_excel(writer, sheet_name='Worksheet 3', index=False)
                    print(f"  Created empty 'Worksheet 3'")

                # Create empty Worksheet if it doesn't exist
                if 'Worksheet' not in template_sheets:
                    empty_df_blank = pd.DataFrame()
                    empty_df_blank.to_excel(writer, sheet_name='Worksheet', index=False)
                    print(f"  Created empty 'Worksheet'")

            created_files.append(output_file)
            print(f"  Successfully created: {output_file}")

        print(f"\nSuccessfully created {len(created_files)} workbook(s):")
        for i, file_path in enumerate(created_files, 1):
            print(f"  {i}. {file_path}")

        return True
        
    except Exception as e:
        print(f"Error processing files: {e}")
        import traceback
        traceback.print_exc()
        return False

def process_directory_pattern(base_path="H:\\isg", pattern="*-20*"):
    """
    Process directories matching the pattern (e.g., ICC-2025, GAP-2026, etc.)
    
    Args:
        base_path (str): Base directory path
        pattern (str): Directory pattern to match
    """
    try:
        # Find directories matching the pattern
        search_pattern = os.path.join(base_path, pattern)
        matching_dirs = glob.glob(search_pattern)
        
        print(f"Found {len(matching_dirs)} directories matching pattern '{pattern}':")
        for dir_path in matching_dirs:
            print(f"  - {dir_path}")
        
        for dir_path in matching_dirs:
            sit_path = os.path.join(dir_path, "sit")
            if os.path.exists(sit_path):
                print(f"\n{'='*60}")
                print(f"Processing directory: {dir_path}")
                print(f"{'='*60}")
                
                # Find Excel files in sit folder
                excel_files = glob.glob(os.path.join(sit_path, "*.xlsx"))
                if excel_files:
                    excel_file = excel_files[0]  # Use first Excel file found
                    print(f"Found template Excel file: {excel_file}")
                    
                    # Look for CSV files in the same directory or parent directory
                    csv_files = glob.glob(os.path.join(sit_path, "*.csv"))
                    if not csv_files:
                        # Check parent directory
                        csv_files = glob.glob(os.path.join(dir_path, "*.csv"))
                    
                    if csv_files:
                        print(f"Found {len(csv_files)} CSV files:")
                        for csv_file in csv_files:
                            print(f"  - {csv_file}")
                        
                        # Process each CSV file
                        for csv_file in csv_files:
                            print(f"\nProcessing CSV: {os.path.basename(csv_file)}")
                            success = copy_csv_to_multiple_workbooks(csv_file, excel_file)
                            if success:
                                print(f"Successfully processed {os.path.basename(csv_file)}")
                            else:
                                print(f"Failed to process {os.path.basename(csv_file)}")
                    else:
                        print(f"No CSV files found in {sit_path} or {dir_path}")
                else:
                    print(f"No Excel files found in {sit_path}")
            else:
                print(f"'sit' folder not found in {dir_path}")
    
    except Exception as e:
        print(f"Error processing directories: {e}")
        import traceback
        traceback.print_exc()

def main():
    print("=" * 60)
    print("CSV to Multiple Excel Workbooks Converter")
    print("=" * 60)
    print()

    # Ask user for mode selection
    print("Select mode:")
    print("1. Process single CSV file with template")
    print("2. Auto-discover directories (pattern-based)")
    print()

    mode = input("Enter your choice (1 or 2): ").strip()

    if mode == "2":
        # Auto-discover mode
        print()
        print("Auto-Discovery Mode")
        print("-" * 60)

        base_path = input("Enter base path [default: H:\\isg]: ").strip()
        if not base_path:
            base_path = "H:\\isg"

        pattern = input("Enter directory pattern [default: *-20*]: ").strip()
        if not pattern:
            pattern = "*-20*"

        print()
        print(f"Auto-discovering directories in '{base_path}' with pattern '{pattern}'...")
        process_directory_pattern(base_path, pattern)

    elif mode == "1":
        # Single file mode
        print()
        print("Single File Processing Mode")
        print("-" * 60)

        csv_file = input("Enter path to CSV file: ").strip()
        if not csv_file:
            print("Error: CSV file path is required")
            return

        # Remove quotes if user pasted path with quotes
        csv_file = csv_file.strip('"').strip("'")

        if not os.path.exists(csv_file):
            print(f"Error: CSV file not found: {csv_file}")
            return

        excel_file = input("Enter path to template Excel file: ").strip()
        if not excel_file:
            print("Error: Excel template file path is required")
            return

        # Remove quotes if user pasted path with quotes
        excel_file = excel_file.strip('"').strip("'")

        if not os.path.exists(excel_file):
            print(f"Error: Excel template file not found: {excel_file}")
            return

        max_rows_input = input("Enter maximum rows per workbook [default: 10000]: ").strip()
        if max_rows_input:
            try:
                max_rows = int(max_rows_input)
            except ValueError:
                print("Invalid number, using default: 10000")
                max_rows = 10000
        else:
            max_rows = 10000

        print()
        print("Processing single CSV to Excel workbooks...")
        success = copy_csv_to_multiple_workbooks(csv_file, excel_file, max_rows)

        if success:
            print()
            print("=" * 60)
            print("Processing completed successfully!")
            print("=" * 60)
        else:
            print()
            print("=" * 60)
            print("Processing failed!")
            print("=" * 60)
    else:
        print("Invalid choice. Please run the script again and select 1 or 2.")

if __name__ == "__main__":
    main()
