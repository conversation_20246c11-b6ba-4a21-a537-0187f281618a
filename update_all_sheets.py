import pandas as pd
import openpyxl
from openpyxl.styles import <PERSON><PERSON>, PatternFill, Alignment

# Load the existing workbook
wb = openpyxl.load_workbook('keyword_sheets.xlsx')

# Get all sheet names except Summary
all_sheets = [sheet for sheet in wb.sheetnames if sheet != 'Summary']

print(f"Updating {len(all_sheets)} worksheets with [TITLE] OR format...")

# Dictionary to store updated keyword counts
updated_counts = {}

# Process each worksheet
for sheet_name in all_sheets:
    print(f"Processing: {sheet_name}")
    
    ws = wb[sheet_name]
    
    # Get the main keyword from the header (first row, second column)
    main_keyword = ws['B1'].value
    if main_keyword and main_keyword != 'Keyword':
        main_keyword = main_keyword.strip()
    else:
        # If no header, derive from sheet name
        main_keyword = sheet_name.replace('_', ' ')
    
    # Clear existing content (keep header)
    for row in ws.iter_rows(min_row=2, max_row=ws.max_row):
        for cell in row:
            cell.value = None
    
    # Create the formatted keyword with [TITLE] OR
    formatted_keyword = f'"{main_keyword}" [TITLE]'
    
    # Add the single formatted keyword
    ws['A2'] = 1  # Serial number
    ws['B2'] = formatted_keyword
    
    # Apply formatting
    ws['A2'].font = Font(name='Arial', size=10)
    ws['B2'].font = Font(name='Arial', size=10)
    
    # Store count for summary update
    updated_counts[main_keyword] = 1

print("\nUpdating Summary sheet...")

# Update the Summary sheet
summary_ws = wb['Summary']
for row in summary_ws.iter_rows(min_row=2, max_row=summary_ws.max_row):
    main_keyword = row[0].value
    if main_keyword in updated_counts:
        row[2].value = updated_counts[main_keyword]  # Update keyword count

# Save the workbook
wb.save('keyword_sheets.xlsx')

print(f"\n✅ Successfully updated all {len(all_sheets)} worksheets!")
print("Each worksheet now contains a single keyword in [TITLE] format")
print("\nSample of updated keywords:")
for i, (keyword, count) in enumerate(list(updated_counts.items())[:10]):
    print(f"  {i+1}. \"{keyword}\" [TITLE]")

if len(updated_counts) > 10:
    print(f"  ... and {len(updated_counts) - 10} more worksheets")
