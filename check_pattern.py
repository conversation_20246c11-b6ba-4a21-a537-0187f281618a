import pandas as pd

df = pd.read_excel('DBMS Working Sheet(keywords list).xlsx')
derma_rows = df[df['Main Keyword'] == 'Dermatology']
for idx, row in derma_rows.iterrows():
    sub_kw = str(row['Sub Keywords'])
    if '[Title]' in sub_kw:
        print('Original text:')
        print(repr(sub_kw[:200]))
        print()
        print('Looking for patterns:')
        print(f'Contains [Title]OR: {"[Title]OR" in sub_kw}')
        print(f'Contains [Title] OR: {"[Title] OR" in sub_kw}')
        break
