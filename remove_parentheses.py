import pandas as pd
import openpyxl
from openpyxl.styles import <PERSON>ont, PatternFill

# Load the existing workbook
wb = openpyxl.load_workbook('keyword_sheets.xlsx')

# Get all sheet names except Summary
all_sheets = [sheet for sheet in wb.sheetnames if sheet != 'Summary']

print(f"Removing parentheses from {len(all_sheets)} worksheets...")
print("Looking for keywords with '[TITLE] OR' and '[TI] OR' formats...")

updated_sheets = 0
total_updates = 0

# Process each worksheet
for sheet_name in all_sheets:
    ws = wb[sheet_name]
    sheet_updated = False
    
    # Check all rows with data
    for row_num in range(2, ws.max_row + 1):
        keyword_cell = ws[f'B{row_num}']
        if keyword_cell.value:
            original_keyword = str(keyword_cell.value)
            
            # Check if the keyword contains [TITLE] OR or [TI] OR
            if '[TITLE] OR' in original_keyword or '[TI] OR' in original_keyword or '[TITLE]' in original_keyword or '[TI]' in original_keyword:
                # Remove parentheses but keep brackets
                cleaned_keyword = original_keyword.replace('(', '').replace(')', '')
                
                # Only update if there was a change
                if cleaned_keyword != original_keyword:
                    keyword_cell.value = cleaned_keyword
                    print(f"  {sheet_name}: '{original_keyword}' → '{cleaned_keyword}'")
                    sheet_updated = True
                    total_updates += 1
    
    if sheet_updated:
        updated_sheets += 1

# Save the workbook
wb.save('keyword_sheets.xlsx')

print(f"\n✅ Processing complete!")
print(f"📊 Updated {updated_sheets} worksheets")
print(f"🔧 Made {total_updates} total keyword updates")
print(f"📝 Removed parentheses while preserving [TITLE] and [TI] brackets")

if total_updates == 0:
    print("\n💡 No parentheses found in keywords with [TITLE] OR or [TI] OR formats")
    print("   All keywords are already clean!")
