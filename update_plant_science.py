import pandas as pd
import openpyxl
from openpyxl.styles import <PERSON><PERSON>, PatternFill, Alignment

# Define the new Plant Science keywords with [TITLE] OR format
new_plant_science_keywords = [
    '"Plant Science" [TITLE] OR',
    '"Plant Physiology" [TITLE] OR', 
    '"Plant Pathology" [TITLE] OR',
    '"Botany" [TITLE] OR',
    '"Plant Growth" [TITLE] OR',
    '"Plant Genetics" [TITLE] OR',
    '"Plant Development" [TITLE] OR'
]

# Remove the last OR from the final keyword
new_plant_science_keywords[-1] = '"Plant Development" [TITLE]'

print("Updating Plant Science worksheet with new keywords...")

# Load the existing workbook
wb = openpyxl.load_workbook('keyword_sheets.xlsx')

# Access the Plant_Science worksheet
ws = wb['Plant_Science']

# Clear existing content (keep header)
for row in ws.iter_rows(min_row=2, max_row=ws.max_row):
    for cell in row:
        cell.value = None

# Add new keywords
for i, keyword in enumerate(new_plant_science_keywords, start=2):
    ws[f'A{i}'] = i-1  # Serial number
    ws[f'B{i}'] = keyword
    
    # Apply formatting
    ws[f'A{i}'].font = Font(name='Arial', size=10)
    ws[f'B{i}'].font = Font(name='Arial', size=10)
    
    # Alternate row colors
    if (i-2) % 2 == 1:  # Every other row
        fill = PatternFill(start_color='F2F2F2', end_color='F2F2F2', fill_type='solid')
        ws[f'A{i}'].fill = fill
        ws[f'B{i}'].fill = fill

# Update the Summary sheet
summary_ws = wb['Summary']
for row in summary_ws.iter_rows(min_row=2, max_row=summary_ws.max_row):
    if row[0].value == 'Plant Science':
        row[2].value = len(new_plant_science_keywords)  # Update keyword count
        break

# Save the workbook
wb.save('keyword_sheets.xlsx')

print(f"✅ Successfully updated Plant Science worksheet with {len(new_plant_science_keywords)} keywords")
print("\nNew Plant Science keywords:")
for i, keyword in enumerate(new_plant_science_keywords, 1):
    print(f"{i}. {keyword}")
