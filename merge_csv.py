import os
import pandas as pd
import glob
import sys
import chardet
import re
from datetime import datetime

try:
    import unicodedata
    UNICODEDATA_AVAILABLE = True
except ImportError:
    UNICODEDATA_AVAILABLE = False

def clean_name(name: str) -> str:
    """
    Clean name by removing text after the first comma.

    Args:
        name: Name string to clean

    Returns:
        Cleaned name string
    """
    return str(name).split(',')[0].strip()

def fix_character_encoding(text: str) -> str:
    """
    Fix common character encoding issues in text.

    Args:
        text: Text string to fix

    Returns:
        Fixed text string with proper characters
    """
    if not isinstance(text, str) or text in ['nan', 'None', '']:
        return text

    # Comprehensive character replacement mapping for all languages
    replacements = {
        # Common punctuation issues
        'â€™': "'",      # Right single quotation mark
        'â€œ': '"',      # Left double quotation mark
        'â€': '"',       # Right double quotation mark
        'â€"': '–',      # En dash
        'â€"': '—',      # Em dash
        'â€¦': '…',      # Horizontal ellipsis

        # Latin characters with diacritics (Ã encoding issues)
        'Ã¼': 'ü',       # u with diaeresis
        'Ã¡': 'á',       # a with acute
        'Ã©': 'é',       # e with acute
        'Ã­': 'í',       # i with acute
        'Ã³': 'ó',       # o with acute
        'Ãº': 'ú',       # u with acute
        'Ã±': 'ñ',       # n with tilde
        'Ã§': 'ç',       # c with cedilla
        'Ã ': 'à',       # a with grave
        'Ã¨': 'è',       # e with grave
        'Ã¬': 'ì',       # i with grave
        'Ã²': 'ò',       # o with grave
        'Ã¹': 'ù',       # u with grave
        'Ã¢': 'â',       # a with circumflex
        'Ãª': 'ê',       # e with circumflex
        'Ã®': 'î',       # i with circumflex
        'Ã´': 'ô',       # o with circumflex
        'Ã»': 'û',       # u with circumflex
        'Ã¤': 'ä',       # a with diaeresis
        'Ã«': 'ë',       # e with diaeresis
        'Ã¯': 'ï',       # i with diaeresis
        'Ã¶': 'ö',       # o with diaeresis
        'Ã¿': 'ÿ',       # y with diaeresis
        'Ã…': 'Å',       # A with ring above
        'Ã¥': 'å',       # a with ring above
        'Ã†': 'Æ',       # AE ligature
        'Ã¦': 'æ',       # ae ligature
        'Ã˜': 'Ø',       # O with stroke
        'Ã¸': 'ø',       # o with stroke
        'Ã': 'Ý',        # Y with acute
        'Ã½': 'ý',       # y with acute
        'Ãƒ': 'Ã',       # A with tilde
        'Ã£': 'ã',       # a with tilde
        'Ã•': 'Õ',       # O with tilde
        'Ãµ': 'õ',       # o with tilde

        # Fraction character encoding issues (1⁄4 = ¼, etc.)
        'A1⁄4': 'Ä',     # A with diaeresis
        'O1⁄4': 'Ö',     # O with diaeresis
        'U1⁄4': 'Ü',     # U with diaeresis
        'a1⁄4': 'ä',     # a with diaeresis
        'o1⁄4': 'ö',     # o with diaeresis
        'u1⁄4': 'ü',     # u with diaeresis
        'ss1⁄4': 'ß',    # German sharp s

        # Specific name fixes (common patterns)
        'TA1⁄4lay': 'Tülay',      # Turkish name
        'KA1⁄4hnel': 'Kühnel',    # German name
        'MA1⁄4ller': 'Müller',    # German name
        'BA1⁄4rger': 'Bürger',    # German name
        'GA1⁄4nther': 'Günther',  # German name
        'HA1⁄4bner': 'Hübner',    # German name
        'KA1⁄4hn': 'Kühn',        # German name
        'LA1⁄4tke': 'Lütke',      # German name
        'SA1⁄4ss': 'Süß',         # German name
        'WA1⁄4rth': 'Würth',      # German name

        # Cyrillic characters (common encoding issues)
        'Ð°': 'а',       # Cyrillic small letter a
        'Ð±': 'б',       # Cyrillic small letter be
        'Ð²': 'в',       # Cyrillic small letter ve
        'Ð³': 'г',       # Cyrillic small letter ghe
        'Ð´': 'д',       # Cyrillic small letter de
        'Ðµ': 'е',       # Cyrillic small letter ie
        'Ð¶': 'ж',       # Cyrillic small letter zhe
        'Ð·': 'з',       # Cyrillic small letter ze
        'Ð¸': 'и',       # Cyrillic small letter i
        'Ð¹': 'й',       # Cyrillic small letter short i
        'Ðº': 'к',       # Cyrillic small letter ka
        'Ð»': 'л',       # Cyrillic small letter el
        'Ð¼': 'м',       # Cyrillic small letter em
        'Ð½': 'н',       # Cyrillic small letter en
        'Ð¾': 'о',       # Cyrillic small letter o
        'Ð¿': 'п',       # Cyrillic small letter pe
        'Ñ€': 'р',       # Cyrillic small letter er
        'Ñ': 'с',        # Cyrillic small letter es
        'Ñ‚': 'т',       # Cyrillic small letter te
        'Ñƒ': 'у',       # Cyrillic small letter u
        'Ñ„': 'ф',       # Cyrillic small letter ef
        'Ñ…': 'х',       # Cyrillic small letter ha
        'Ñ†': 'ц',       # Cyrillic small letter tse
        'Ñ‡': 'ч',       # Cyrillic small letter che
        'Ñˆ': 'ш',       # Cyrillic small letter sha
        'Ñ‰': 'щ',       # Cyrillic small letter shcha
        'ÑŠ': 'ъ',       # Cyrillic hard sign
        'Ñ‹': 'ы',       # Cyrillic small letter yeru
        'ÑŒ': 'ь',       # Cyrillic soft sign
        'Ñ': 'э',        # Cyrillic small letter e
        'ÑŽ': 'ю',       # Cyrillic small letter yu
        'Ñ': 'я',        # Cyrillic small letter ya

        # Greek characters (common encoding issues)
        'Î±': 'α',       # Greek small letter alpha
        'Î²': 'β',       # Greek small letter beta
        'Î³': 'γ',       # Greek small letter gamma
        'Î´': 'δ',       # Greek small letter delta
        'Îµ': 'ε',       # Greek small letter epsilon
        'Î¶': 'ζ',       # Greek small letter zeta
        'Î·': 'η',       # Greek small letter eta
        'Î¸': 'θ',       # Greek small letter theta
        'Î¹': 'ι',       # Greek small letter iota
        'Îº': 'κ',       # Greek small letter kappa
        'Î»': 'λ',       # Greek small letter lamda
        'Î¼': 'μ',       # Greek small letter mu
        'Î½': 'ν',       # Greek small letter nu
        'Î¾': 'ξ',       # Greek small letter xi
        'Î¿': 'ο',       # Greek small letter omicron
        'Ï€': 'π',       # Greek small letter pi
        'Ï': 'ρ',        # Greek small letter rho
        'Ï‚': 'ς',       # Greek small letter final sigma
        'Ïƒ': 'σ',       # Greek small letter sigma
        'Ï„': 'τ',       # Greek small letter tau
        'Ï…': 'υ',       # Greek small letter upsilon
        'Ï†': 'φ',       # Greek small letter phi
        'Ï‡': 'χ',       # Greek small letter chi
        'Ïˆ': 'ψ',       # Greek small letter psi
        'Ï‰': 'ω',       # Greek small letter omega

        # Additional common patterns
        '1⁄4': 'ü',      # Generic fraction to ü replacement
        '1⁄2': 'ö',      # Generic fraction to ö replacement
        '3⁄4': 'ä',      # Generic fraction to ä replacement

        # Clean up pandas artifacts
        'nan': '',
        'None': '',
        'NaN': ''
    }

    # Apply direct replacements
    for old, new in replacements.items():
        text = text.replace(old, new)

    # Apply regex-based pattern matching for remaining fraction character issues
    text = re.sub(r'([A-Za-z])1⁄4', r'\1ü', text)
    text = re.sub(r'([A-Za-z])1⁄2', r'\1ö', text)
    text = re.sub(r'([A-Za-z])3⁄4', r'\1ä', text)

    # Normalize Unicode characters if available
    if UNICODEDATA_AVAILABLE:
        try:
            text = unicodedata.normalize('NFC', text)
        except:
            pass

    return text

def merge_csv_files(input_dir=None, output_file=None, keep_headers=True):
    """
    Merge all CSV files in the specified directory into a single CSV file.

    Parameters:
    - input_dir: Directory containing CSV files to merge
    - output_file: Path to the output merged CSV file (default: merged_YYYYMMDD.csv in input_dir)
    - keep_headers: Whether to keep headers from all files (True) or just the first file (False)
    """
    # If no input directory is provided, ask for it
    if input_dir is None:
        input_dir = input('Enter the directory path containing CSV files to merge: ')
        # Remove quotes if the user included them
        input_dir = input_dir.strip('"\'')

    # Change to the input directory
    try:
        original_dir = os.getcwd()
        os.chdir(input_dir)
        print(f"Changed working directory to: {input_dir}")
    except Exception as e:
        print(f"Error: Could not change to directory {input_dir}")
        print(f"Error details: {str(e)}")
        return False

    # Create merged folder if it doesn't exist
    output_folder = os.path.join(input_dir, "merged")
    if not os.path.exists(output_folder):
        try:
            os.makedirs(output_folder)
            print(f"Created merged folder: {output_folder}")
        except Exception as e:
            print(f"Warning: Could not create merged folder: {str(e)}")
            # Fall back to input directory if merged folder creation fails
            output_folder = input_dir

    # Try to extract conference segment name from the path
    csn = None
    match = re.search(r"\\([\w\s-]+\d{4})\\?", input_dir)
    if match:
        csn = match.group(1)
        print(f"Conference Segment Name (CSN) extracted: {csn}")
    else:
        print("Conference segment name not found in path. Will use generic filename.")

    # If no output file is specified, ask for it or create a default one
    if output_file is None:
        use_default = input('Do you want to specify an output file path? (y/n): ').lower().startswith('y')
        if use_default:
            output_file = input('Enter the output file path: ')
            # Remove quotes if the user included them
            output_file = output_file.strip('"\'')
        else:
            today = datetime.now().strftime("%Y%m%d")
            # Use the output folder for the default file
            if csn:
                output_file = os.path.join(output_folder, f"{csn}_merged_{today}.csv")
            else:
                output_file = os.path.join(output_folder, f"merged_{today}.csv")
            print(f"Using default output file in merged folder: {output_file}")

    # Get a list of all CSV and Excel files in the directory
    csv_files = glob.glob("*.csv")
    excel_files = glob.glob("*.xlsx") + glob.glob("*.xls")

    all_files = csv_files + excel_files

    if not all_files:
        print(f"No CSV or Excel files found in {input_dir}")
        os.chdir(original_dir)  # Change back to original directory
        return False

    print(f"Found {len(csv_files)} CSV files and {len(excel_files)} Excel files to merge")

    # Initialize an empty list to store DataFrames
    all_dfs = []
    total_rows = 0

    # Read each file and append to the list
    for i, file in enumerate(all_files, 1):
        try:
            print(f"Processing file {i}/{len(all_files)}: {file}")

            # Check if the file is an Excel file
            if file.lower().endswith(('.xlsx', '.xls')):
                print(f"  - Detected Excel file format")
                # Try to read the Excel file
                try:
                    df = pd.read_excel(file, dtype=str, keep_default_na=False, na_filter=False)

                    # Apply character encoding fixes to all string columns
                    for col in df.columns:
                        if df[col].dtype == 'object':
                            df[col] = df[col].apply(fix_character_encoding)

                    rows = len(df)
                    total_rows += rows
                    print(f"  - Read {rows} rows successfully from Excel file with character preservation")
                    all_dfs.append(df)
                    continue  # Skip to the next file
                except Exception as excel_error:
                    print(f"  - Error reading Excel file: {str(excel_error)}")
                    print(f"  - Will try to read as CSV instead")

            # For CSV files or if Excel reading failed
            # Detect the file encoding using chardet
            with open(file, 'rb') as rawdata:
                # Read more data for better detection
                result = chardet.detect(rawdata.read(100000))
            detected_encoding = result['encoding']
            confidence = result['confidence']
            print(f"  - Detected encoding: {detected_encoding} (confidence: {confidence:.2f})")

            # Enhanced encoding preference list
            preferred_encodings = ['utf-8-sig', 'utf-8', 'cp1252', 'iso-8859-1', 'iso-8859-15', 'cp1250', 'cp1251', 'latin-1']

            # Use the detected encoding if it's reliable
            if confidence >= 0.8 and detected_encoding:
                # Map some common detected encodings to preferred ones
                encoding_map = {
                    'ascii': 'utf-8-sig',
                    'utf-8': 'utf-8-sig',
                    'windows-1252': 'cp1252',
                    'windows-1250': 'cp1250',
                    'windows-1251': 'cp1251',
                    'iso-8859-1': 'iso-8859-1'
                }
                encoding_to_use = encoding_map.get(detected_encoding.lower(), detected_encoding)
            else:
                encoding_to_use = 'utf-8-sig'
                print(f"  - Low confidence detection, using {encoding_to_use}")

            # Try to read the file with the detected encoding and character preservation
            df = pd.read_csv(
                file,
                encoding=encoding_to_use,
                on_bad_lines='skip',
                low_memory=False,
                dtype=str,
                keep_default_na=False,
                na_filter=False
            )

            # Apply character encoding fixes to all string columns
            for col in df.columns:
                if df[col].dtype == 'object':
                    df[col] = df[col].apply(fix_character_encoding)

            rows = len(df)
            total_rows += rows
            print(f"  - Read {rows} rows successfully with {encoding_to_use} encoding and character preservation")
            all_dfs.append(df)

        except Exception as e:
            print(f"  - Error reading {file} with detected encoding: {str(e)}")
            try:
                # Try with alternative encodings with character preservation
                fallback_encodings = ['utf-8-sig', 'utf-8', 'cp1252', 'iso-8859-1', 'iso-8859-15', 'cp1250', 'cp1251', 'latin-1']
                success = False

                for alt_encoding in fallback_encodings:
                    try:
                        print(f"  - Trying with {alt_encoding} encoding")
                        df = pd.read_csv(
                            file,
                            encoding=alt_encoding,
                            on_bad_lines='skip',
                            low_memory=False,
                            dtype=str,
                            keep_default_na=False,
                            na_filter=False
                        )

                        # Apply character encoding fixes
                        for col in df.columns:
                            if df[col].dtype == 'object':
                                df[col] = df[col].apply(fix_character_encoding)

                        rows = len(df)
                        total_rows += rows
                        print(f"  - Read {rows} rows successfully with {alt_encoding} encoding and character preservation")
                        all_dfs.append(df)
                        success = True
                        break  # Break the loop if successful
                    except Exception as e3:
                        continue  # Try the next encoding

                if not success:
                    print(f"  - Failed to read file with any encoding")
            except Exception as e2:
                print(f"  - Failed to read file: {str(e2)}")

    if not all_dfs:
        print("No data could be read from any CSV files")
        os.chdir(original_dir)  # Change back to original directory
        return False

    # Standardize column names before combining
    print("Standardizing column names...")
    for i, df in enumerate(all_dfs):
        # Check if the DataFrame has a 'Name' column but no 'Author Name' column
        if 'Name' in df.columns and 'Author Name' not in df.columns:
            print(f"  - Renaming 'Name' to 'Author Name' in file {i+1}")
            df.rename(columns={'Name': 'Author Name'}, inplace=True)

        # Clean names by removing text after comma and apply character preservation for specified columns
        columns_to_clean = ['col1', 'name', 'author name', 'Author Name']
        for col in columns_to_clean:
            # Check for exact match and case-insensitive match
            matching_cols = [c for c in df.columns if c.lower() == col.lower()]
            for matching_col in matching_cols:
                if matching_col in df.columns:
                    print(f"  - Cleaning commas and preserving characters in '{matching_col}' column in file {i+1}")
                    df[matching_col] = df[matching_col].apply(lambda x: fix_character_encoding(clean_name(x)))

    # Combine all DataFrames
    print("Combining all data...")
    if keep_headers:
        # Concatenate all DataFrames, keeping all headers
        combined_df = pd.concat(all_dfs, ignore_index=True)
    else:
        # Use the first DataFrame's columns and append data from others
        combined_df = all_dfs[0]
        for df in all_dfs[1:]:
            combined_df = pd.concat([combined_df, df], ignore_index=True)

    # Check for important columns
    important_columns = []
    email_col = None
    name_cols = []

    for col in combined_df.columns:
        col_lower = col.lower()
        if col_lower == 'email':
            email_col = col
            important_columns.append(col)
        # Now we should only have 'Author Name' for name column, but check others just in case
        if col_lower in ['col1', 'author name', 'name', 'full name', 'author']:
            name_cols.append(col)
            important_columns.append(col)

    # Remove duplicates based on Email column if it exists
    if email_col:
        original_count = len(combined_df)
        print(f"Checking for duplicate emails in column '{email_col}'...")
        # Drop duplicates, keeping the first occurrence
        combined_df = combined_df.drop_duplicates(subset=[email_col], keep='first')
        duplicate_count = original_count - len(combined_df)
        if duplicate_count > 0:
            print(f"Removed {duplicate_count} duplicate email entries")
        else:
            print("No duplicate emails found")
    else:
        print("Warning: No 'Email' column found for deduplication")

    # Report on empty values in name columns but don't remove them
    if name_cols:
        empty_count = combined_df[name_cols].isna().any(axis=1).sum()
        print(f"Found {empty_count} rows with empty values in name columns: {', '.join(name_cols)}")
        print("Note: Rows with empty name values are kept in the output file")

        # If we still have both 'Name' and 'Author Name' columns, merge them
        if 'Name' in combined_df.columns and 'Author Name' in combined_df.columns:
            print("Merging 'Name' and 'Author Name' columns...")
            # Fill NaN values in 'Author Name' with values from 'Name'
            combined_df['Author Name'] = combined_df['Author Name'].fillna(combined_df['Name'])
            # Drop the 'Name' column
            combined_df.drop(columns=['Name'], inplace=True)
            print("'Name' column merged into 'Author Name' and removed")
    else:
        print("Warning: No name columns found ('Author Name', 'Name', etc.)")

    # Apply final character preservation to all string columns before saving
    print("Applying final character preservation...")
    for col in combined_df.columns:
        if combined_df[col].dtype == 'object':
            combined_df[col] = combined_df[col].apply(fix_character_encoding)
            # Normalize Unicode characters if available
            if UNICODEDATA_AVAILABLE:
                try:
                    combined_df[col] = combined_df[col].apply(
                        lambda x: unicodedata.normalize('NFC', x) if isinstance(x, str) and x else x
                    )
                except:
                    pass

    # Write the combined DataFrame to a CSV file with UTF-8-SIG encoding (includes BOM)
    print(f"Writing {len(combined_df)} rows to {output_file}")
    print(f"Using UTF-8-SIG encoding for output file (includes BOM for Excel compatibility)")
    try:
        combined_df.to_csv(output_file, index=False, encoding='utf-8-sig', errors='replace')
        print("File saved successfully with comprehensive character preservation")

        # Verify the saved file by reading a sample
        try:
            verification_df = pd.read_csv(output_file, encoding='utf-8-sig', nrows=10)
            print("File verification: Successfully read back saved file with UTF-8-sig")

            # Check for any remaining character issues in the verification sample
            char_issues_found = False
            for col in verification_df.columns:
                if verification_df[col].dtype == 'object':
                    sample_text = ' '.join(verification_df[col].astype(str).head(5))
                    if any(seq in sample_text for seq in ['â€™', 'â€œ', 'â€', 'Ã¼', 'Ã¡', 'TA1⁄4', 'KA1⁄4']):
                        char_issues_found = True
                        break

            if char_issues_found:
                print("Warning: Some character encoding issues may still exist")
            else:
                print("Character verification: No encoding artifacts detected")

        except Exception as verify_error:
            print(f"File verification warning: {str(verify_error)}")

    except Exception as save_error:
        print(f"Error saving file: {str(save_error)}")
        # Try a simpler save approach as fallback
        try:
            print("Attempting fallback save method...")
            combined_df.to_csv(output_file, index=False, encoding='utf-8-sig')
            print("Fallback save successful")
        except Exception as fallback_error:
            print(f"Fallback save also failed: {str(fallback_error)}")
            return False

    print(f"Successfully merged {len(csv_files)} CSV files with a total of {total_rows} rows")
    print(f"Output file: {output_file}")

    # After successfully merging files, create README.md
    # COMMENTED OUT: README.md file creation disabled
    # readme_content = f"""# Merge Details
    #
    # ## Merge Summary
    # - **Date of Merge:** {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
    # - **Total Files Merged:** {len(all_files)}
    # - **Total CSV Files:** {len(csv_files)}
    # - **Total Excel Files:** {len(excel_files)}
    # - **Total Rows Processed:** {total_rows}
    # - **Output File:** {os.path.basename(output_file)}
    #
    # ## Files Merged
    # ### CSV Files:
    # {chr(10).join([f"- {file}" for file in csv_files])}
    #
    # ### Excel Files:
    # {chr(10).join([f"- {file}" for file in excel_files])}
    #
    # ## Processing Details
    # - Headers kept from all files: {keep_headers}
    # - Duplicate emails removed: {duplicate_count if 'duplicate_count' in locals() else 'N/A'}
    # - Empty name entries: {empty_count if 'empty_count' in locals() else 'N/A'}
    #
    # ## Column Standardization
    # - 'Name' columns merged into 'Author Name' where applicable
    # - Email deduplication performed (first occurrence kept)
    # """
    #
    # # Write README.md to the merged folder
    # readme_path = os.path.join(output_folder, "README.md")
    # try:
    #     with open(readme_path, 'w', encoding='utf-8') as f:
    #         f.write(readme_content)
    #     print(f"Created merge documentation: {readme_path}")
    # except Exception as e:
    #     print(f"Warning: Could not create README.md: {str(e)}")

    # Change back to original directory
    os.chdir(original_dir)
    return True

if __name__ == "__main__":
    # Check if directory is provided as command line argument
    if len(sys.argv) > 1:
        input_dir = sys.argv[1]
        output_file = sys.argv[2] if len(sys.argv) > 2 else None
        merge_csv_files(input_dir, output_file)
    else:
        # Use interactive input
        merge_csv_files()
        print("\nPress Enter to exit...")
        input()


