import os
import glob
import pandas as pd
import numpy as np
import warnings
import random

# Suppress pandas warnings - compatible with both old and new pandas versions
try:
    # Try the new location first (pandas >= 1.5.0)
    from pandas.core.common import SettingWithCopyWarning
    warnings.simplefilter(action="ignore", category=SettingWithCopyWarning)
except ImportError:
    try:
        # Try the old location (pandas < 1.5.0)
        from pandas.errors import SettingWithCopyWarning
        warnings.simplefilter(action="ignore", category=SettingWithCopyWarning)
    except (ImportError, AttributeError):
        # If neither works, just ignore pandas warnings in general
        warnings.filterwarnings("ignore", message=".*SettingWithCopyWarning.*")

def clean_name(name: str) -> str:
    """
    Clean name by removing text after the first comma.

    Args:
        name: Name string to clean

    Returns:
        Cleaned name string
    """
    return str(name).split(',')[0].strip()

def generate_subject_lines(df: pd.DataFrame, csn: str) -> pd.DataFrame:
    """
    Generate random subject lines for each record.

    Args:
        df: DataFrame with records
        csn: Conference segment name

    Returns:
        DataFrame with added subject lines
    """
    print("Generating subject lines...")

    # List of subject line templates
    subj_list = [
        'Publish Your Work in [CCT_CSNAME] – Invitation to Submit',
        'Your Research Is Valued – Submit to [CCT_CSNAME]',
        'Invitation to Contribute to [CCT_CSNAME]\'s Upcoming Issue',
        'Join Our Esteemed Authors – Submit Your Manuscript Today',
        'Exclusive Publishing Opportunity in [CCT_CSNAME]',
        'Special Edition Contribution Request – [CCT_CSNAME]',
        'Your Expertise Is Needed – Manuscript Invitation',
        'An Opportunity to Showcase Your Research in [CCT_CSNAME]',
        'Invitation to Share Your Groundbreaking Work',
        'Publish with Us: Invitation to Submit Your Latest Research',
        'Invitation to Submit: Advance Your Field with [CCT_CSNAME]',
        'Highlight Your Research – Submit to [CCT_CSNAME] Today',
        'Invitation for Collaboration – Contribute to Our Next Issue',
        'Call for Exceptional Manuscripts – [CCT_CSNAME] Wants Yours',
        'Contribute to Scientific Excellence – Publish in [CCT_CSNAME]'
    ]

    # Replace placeholder with actual conference name
    dynamic_subj_list = [subject.replace("[CCT_CSNAME]", csn) for subject in subj_list]

    # Assign random subject lines
    df["Subject"] = pd.Series(
        random.choices(dynamic_subj_list, k=len(df)),
        index=df.index
    )

    return df

path = input("Enter loc: ")
csn = input("Enter csname: ")
include_subject = input("Include subject lines? (y/n): ").strip().lower() == 'y'

os.chdir(path)
csv_files = glob.glob('*.{}'.format('csv'))
csv_files

os.makedirs(("output"), exist_ok=True)

df_concat = pd.concat([pd.read_csv(f) for f in csv_files ], ignore_index=True)
df_concat.rename(columns= {'Author Name':'Name'}, inplace=True)

# Clean names by removing text after comma
df_concat['Name'] = df_concat['Name'].apply(clean_name)

df_concat.dropna(subset='Email', inplace=True)
df_concat.to_csv("./output/"+csn+"-journal_unfiltered.csv", encoding='utf-8-sig', index=False)

df_hb = pd.read_csv("H:/Master Bounces and Unsubs/Master Bounces and Unsubs/Master_Hardbounces.csv", on_bad_lines='skip')
df_unsubs = pd.read_csv("H:/Master Bounces and Unsubs/Master Bounces and Unsubs/Master_Journal_Unsubscribes.csv")

df_concat_unsubs = df_unsubs
df_concat_unsubs[['Email']] = df_concat_unsubs[['Email']].applymap(lambda x:x.lower())

df_concat = pd.read_csv(path+"./output/"+csn+"-journal_unfiltered.csv", low_memory=False)
df_hb_filtered = df_concat[~(df_concat.Email.isin(df_hb.Email))]
df_unsubs_filtered = df_hb_filtered[~(df_hb_filtered.Email.isin(df_concat_unsubs.Email))]
df = df_unsubs_filtered

df.to_csv("./output/"+csn+"-journal_deduped.csv", encoding='utf-8-sig', index=False)
df = pd.read_csv("./output/"+csn+"-journal_deduped.csv", usecols = ["Name", "Email"])
df1 = df.replace(r'^\s*$', np.nan, regex=True)
df2 = df1.fillna('Colleague')
result = df2.drop_duplicates(subset = 'Email')

# Add subject lines if requested
if include_subject:
    print("Adding subject lines...")
    result = generate_subject_lines(result, csn)
    print("Subject lines added successfully")

result.to_csv("./output/"+csn+"-journal_prop_final.csv", mode = 'w+', encoding='utf-8-sig', index=False)
df = pd.read_csv("./output/"+csn+"-journal_prop_final.csv")

print(len(df))

os.remove("./output/"+csn+"-journal_deduped.csv")
os.remove("./output/"+csn+"-journal_unfiltered.csv")
os.replace("./output/"+csn+"-journal_prop_final.csv", "./output/"+csn+"-journal.csv")

print("Completed!")
